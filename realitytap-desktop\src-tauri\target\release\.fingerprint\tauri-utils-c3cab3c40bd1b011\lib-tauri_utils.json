{"rustc": 10895048813736897673, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 1369601567987815722, "path": 12241120027172578749, "deps": [[561782849581144631, "html5ever", false, 11308477217029856252], [1200537532907108615, "url<PERSON><PERSON>n", false, 12797854064370971864], [3060637413840920116, "proc_macro2", false, 2224750471340170475], [3129130049864710036, "memchr", false, 17022076246839158838], [3150220818285335163, "url", false, 8003661020387016638], [3191507132440681679, "serde_untagged", false, 16172905465479084349], [4899080583175475170, "semver", false, 12183985207572930630], [5986029879202738730, "log", false, 9243299641281711331], [6213549728662707793, "serde_with", false, 15386211671964325796], [6262254372177975231, "kuchiki", false, 8336957157522786936], [6606131838865521726, "ctor", false, 9413999893018703200], [6913375703034175521, "schemars", false, 981247787966908288], [7170110829644101142, "json_patch", false, 2064230335387406586], [8269115081296425610, "uuid", false, 9213008617362214269], [8786711029710048183, "toml", false, 1712342395853086187], [9010263965687315507, "http", false, 5218321410908112203], [9451456094439810778, "regex", false, 14808501546914656020], [9689903380558560274, "serde", false, 12432392799593878276], [10806645703491011684, "thiserror", false, 9393090103709299999], [11655476559277113544, "cargo_metadata", false, 18086311629532896720], [11989259058781683633, "dunce", false, 11268025705413442150], [13625485746686963219, "anyhow", false, 7435008003544188016], [14132538657330703225, "brotli", false, 13458612873346433647], [15367738274754116744, "serde_json", false, 7653967626044807545], [15622660310229662834, "walkdir", false, 1977710270525059799], [17146114186171651583, "infer", false, 13843710155270379305], [17155886227862585100, "glob", false, 15036848553281000914], [17186037756130803222, "phf", false, 14526200019349404679], [17990358020177143287, "quote", false, 15310116662474033973]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-c3cab3c40bd1b011\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}