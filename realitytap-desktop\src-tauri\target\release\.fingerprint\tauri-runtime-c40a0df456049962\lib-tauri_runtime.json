{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2040997289075261528, "path": 17452753421783509941, "deps": [[442785307232013896, "build_script_build", false, 9393669993828215039], [3150220818285335163, "url", false, 7399539242944503594], [4143744114649553716, "raw_window_handle", false, 826242152692617864], [7606335748176206944, "dpi", false, 9231594808946547896], [9010263965687315507, "http", false, 537038018071154330], [9689903380558560274, "serde", false, 12791218709341986024], [10806645703491011684, "thiserror", false, 13729077771554084640], [11050281405049894993, "tauri_utils", false, 16407344261864543779], [13116089016666501665, "windows", false, 16525752076480673242], [15367738274754116744, "serde_json", false, 7458677227193029867], [16727543399706004146, "cookie", false, 15644358438724046019]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-c40a0df456049962\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}