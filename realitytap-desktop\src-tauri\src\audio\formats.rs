// Audio format processing utilities
use crate::audio::ffmpeg::{extract_mp4_audio_data_with_ffmpeg, extract_mp4_info_with_ffprobe};
use crate::error::{Error, Result};
use crate::models::audio::AudioInfo;
use hound;
use log;
use std::fs;
use std::path::PathBuf;
use symphonia::core::audio::{AudioBufferRef, Signal};
use symphonia::core::codecs::DecoderOptions;
use symphonia::core::formats::FormatOptions;
use symphonia::core::io::MediaSourceStream;
use symphonia::core::meta::MetadataOptions;
use symphonia::core::probe::Hint;
use symphonia::default::{get_codecs, get_probe};

/// Extract basic audio information from WAV file
pub fn extract_info_from_wav(abs_path: &PathBuf) -> Result<(Vec<f32>, Option<AudioInfo>)> {
    log::info!("进入 WAV 采样分支");
    match hound::WavReader::open(abs_path) {
        Ok(reader) => {
            let spec = reader.spec();
            let audio_info = Some(AudioInfo {
                duration_ms: (reader.duration() as f64 / spec.sample_rate as f64 * 1000.0) as u64,
                sample_rate: spec.sample_rate,
            });

            log::info!("WAV 仅获取音频信息，跳过音频数据处理");
            Ok((vec![0.0], audio_info))
        }
        Err(e) => {
            log::error!("读取 WAV 文件失败: {}", e);
            Ok((vec![], None))
        }
    }
}

/// Extract basic audio information from MP3 file
pub fn extract_info_from_mp3(abs_path: &PathBuf) -> Result<(Vec<f32>, Option<AudioInfo>)> {
    log::info!("进入 MP3 采样分支（symphonia）");

    let file = match fs::File::open(abs_path) {
        Ok(f) => f,
        Err(e) => {
            log::error!("打开 MP3 文件失败: {}", e);
            return Err(Error::Io(format!("打开 MP3 文件失败: {}", e)));
        }
    };

    let mss = MediaSourceStream::new(Box::new(file), Default::default());
    let hint = Hint::new();
    let probed = match get_probe().format(
        &hint,
        mss,
        &FormatOptions::default(),
        &MetadataOptions::default(),
    ) {
        Ok(p) => p,
        Err(e) => {
            log::error!("symphonia 格式探测失败: {}", e);
            return Err(Error::Io(format!("symphonia 格式探测失败: {}", e)));
        }
    };

    let track = match probed.format.default_track() {
        Some(t) => t,
        None => {
            log::error!("未找到默认音轨");
            return Err(Error::Io("未找到默认音轨".to_string()));
        }
    };

    let sample_rate = track.codec_params.sample_rate.unwrap_or(44100);
    let duration_ms = if let Some(n_frames) = track.codec_params.n_frames {
        (n_frames as f64 / sample_rate as f64 * 1000.0) as u64
    } else {
        0 // 无法确定时长
    };

    let audio_info = Some(AudioInfo {
        duration_ms,
        sample_rate,
    });

    Ok((vec![0.0], audio_info))
}

/// Extract basic audio information from MP4 file using ffprobe
pub fn extract_info_from_mp4(abs_path: &PathBuf) -> Result<(Vec<f32>, Option<AudioInfo>)> {
    log::info!("进入 MP4 采样分支（ffprobe）");

    match extract_mp4_info_with_ffprobe(abs_path) {
        Ok(audio_info) => {
            if let Some(info) = &audio_info {
                log::info!(
                    "MP4 音频信息提取成功: duration_ms={}, sample_rate={}",
                    info.duration_ms,
                    info.sample_rate
                );
            }
            Ok((vec![0.0], audio_info))
        }
        Err(e) => {
            log::error!("MP4 音频信息提取失败: {}", e);

            // 提供用户友好的错误信息
            let error_msg = if e.to_string().contains("ffprobe 未找到") {
                format!(
                    "MP4 文件处理需要 FFmpeg 工具包。\n\
                    请安装 FFmpeg 并确保 ffprobe 命令可用。\n\
                    下载地址: https://ffmpeg.org/download.html\n\
                    \n原始错误: {}",
                    e
                )
            } else {
                format!("MP4 文件音频信息提取失败: {}", e)
            };

            Err(Error::Io(error_msg))
        }
    }
}

/// Extract complete audio data from WAV file
pub fn extract_full_audio_data_from_wav(abs_path: &PathBuf) -> Result<(Vec<f32>, AudioInfo)> {
    log::info!("提取完整 WAV 音频数据");
    match hound::WavReader::open(abs_path) {
        Ok(mut reader) => {
            let spec = reader.spec();
            let audio_info = AudioInfo {
                duration_ms: (reader.duration() as f64 / spec.sample_rate as f64 * 1000.0) as u64,
                sample_rate: spec.sample_rate,
            };
            let num_channels = spec.channels as usize;
            let mut all_samples_vec = Vec::new();

            if spec.sample_format == hound::SampleFormat::Float {
                for s in reader.samples::<f32>() {
                    if let Ok(val) = s {
                        all_samples_vec.push(val);
                    }
                }
            } else {
                for s in reader.samples::<i16>() {
                    if let Ok(val) = s {
                        all_samples_vec.push(val as f32 / 32768.0);
                    }
                }
            }

            // 转换为单声道
            let mono_samples: Vec<f32> = if num_channels > 1 {
                all_samples_vec
                    .chunks(num_channels)
                    .map(|chunk| chunk.iter().sum::<f32>() / chunk.len() as f32)
                    .collect()
            } else {
                all_samples_vec
            };

            log::info!("WAV 完整音频数据提取完成，样本数: {}", mono_samples.len());
            Ok((mono_samples, audio_info))
        }
        Err(e) => {
            log::error!("读取 WAV 文件失败: {}", e);
            Err(Error::Io(format!("读取 WAV 文件失败: {}", e)))
        }
    }
}

/// Extract complete audio data from MP3 file
pub fn extract_full_audio_data_from_mp3(abs_path: &PathBuf) -> Result<(Vec<f32>, AudioInfo)> {
    log::info!("提取完整 MP3 音频数据（symphonia）");
    let mut local_samples = Vec::new();
    let file = match fs::File::open(abs_path) {
        Ok(f) => f,
        Err(e) => {
            log::error!("打开 MP3 文件失败: {}", e);
            return Err(Error::Io(format!("打开 MP3 文件失败: {}", e)));
        }
    };

    let mss = MediaSourceStream::new(Box::new(file), Default::default());
    let hint = Hint::new();
    let probed = match get_probe().format(
        &hint,
        mss,
        &FormatOptions::default(),
        &MetadataOptions::default(),
    ) {
        Ok(p) => p,
        Err(e) => {
            log::error!("symphonia 格式探测失败: {}", e);
            return Err(Error::Io(format!("symphonia 格式探测失败: {}", e)));
        }
    };

    let mut format = probed.format;
    let track = match format.default_track() {
        Some(t) => t,
        None => {
            log::error!("未找到默认音轨");
            return Err(Error::Io("未找到默认音轨".to_string()));
        }
    };

    let mut decoder = match get_codecs().make(&track.codec_params, &DecoderOptions::default()) {
        Ok(d) => d,
        Err(e) => {
            log::error!("symphonia 解码器创建失败: {}", e);
            return Err(Error::Io(format!("symphonia 解码器创建失败: {}", e)));
        }
    };

    let mut sample_rate = track.codec_params.sample_rate.unwrap_or(44100);
    let mut channels = track.codec_params.channels.map(|c| c.count()).unwrap_or(1);

    // 解码所有音频数据
    loop {
        match format.next_packet() {
            Ok(packet) => {
                match decoder.decode(&packet) {
                    Ok(audio_buf) => {
                        match audio_buf {
                            AudioBufferRef::F32(buf) => {
                                channels = buf.spec().channels.count();
                                sample_rate = buf.spec().rate;
                                let mono_samples: Vec<f32> = if channels > 1 {
                                    (0..buf.frames())
                                        .map(|i| {
                                            let mut sum = 0.0f32;
                                            for ch in 0..channels {
                                                sum += buf.chan(ch)[i];
                                            }
                                            sum / channels as f32
                                        })
                                        .collect()
                                } else {
                                    buf.chan(0).to_vec()
                                };
                                local_samples.extend(mono_samples);
                            }
                            AudioBufferRef::S16(buf) => {
                                channels = buf.spec().channels.count();
                                sample_rate = buf.spec().rate;
                                let mono_samples: Vec<f32> = if channels > 1 {
                                    (0..buf.frames())
                                        .map(|i| {
                                            let mut sum = 0.0f32;
                                            for ch in 0..channels {
                                                sum += buf.chan(ch)[i] as f32 / 32768.0;
                                            }
                                            sum / channels as f32
                                        })
                                        .collect()
                                } else {
                                    buf.chan(0).iter().map(|&v| v as f32 / 32768.0).collect()
                                };
                                local_samples.extend(mono_samples);
                            }
                            // 处理其他格式...
                            _ => {
                                log::warn!("不支持的音频缓冲区类型");
                            }
                        }
                    }
                    Err(e) => {
                        log::warn!("解码音频帧失败: {}", e);
                    }
                }
            }
            Err(e) => match e {
                symphonia::core::errors::Error::IoError(ref io_error)
                    if io_error.kind() == std::io::ErrorKind::UnexpectedEof =>
                {
                    log::info!("Symphonia: End of file reached during packet reading.");
                    break;
                }
                _ => {
                    log::error!("读取音频包失败: {}", e);
                    return Err(Error::Io(format!("读取音频包失败: {}", e)));
                }
            },
        }
    }

    let duration_ms = if sample_rate > 0 {
        (local_samples.len() as f64 / sample_rate as f64 * 1000.0) as u64
    } else {
        0
    };

    let audio_info = AudioInfo {
        duration_ms,
        sample_rate,
    };

    log::info!("MP3 完整音频数据提取完成，样本数: {}", local_samples.len());
    Ok((local_samples, audio_info))
}

/// Extract complete audio data from MP4 file using ffmpeg
pub fn extract_full_audio_data_from_mp4(abs_path: &PathBuf) -> Result<(Vec<f32>, AudioInfo)> {
    log::info!("使用 ffmpeg 提取 MP4 音频数据");

    match extract_mp4_audio_data_with_ffmpeg(abs_path) {
        Ok(result) => {
            log::info!("MP4 音频数据提取成功");
            Ok(result)
        }
        Err(e) => {
            log::error!("MP4 音频数据提取失败: {}", e);

            // 提供用户友好的错误信息
            let error_msg = if e.to_string().contains("ffmpeg 未找到") {
                format!(
                    "MP4 文件处理需要 FFmpeg 工具包。\n\
                    请安装 FFmpeg 并确保 ffmpeg 命令可用。\n\
                    下载地址: https://ffmpeg.org/download.html\n\
                    \n原始错误: {}",
                    e
                )
            } else {
                format!("MP4 文件处理失败: {}", e)
            };

            Err(Error::Io(error_msg))
        }
    }
}

/// Detect audio format from file extension
pub fn detect_audio_format(file_path: &PathBuf) -> Option<String> {
    file_path
        .extension()
        .and_then(|e| e.to_str())
        .map(|s| s.to_lowercase())
}

/// Extract audio data based on format
pub fn extract_audio_data(abs_path: &PathBuf, full_data: bool) -> Result<(Vec<f32>, AudioInfo)> {
    let format = detect_audio_format(abs_path)
        .ok_or_else(|| Error::NotFound("无法检测音频格式".to_string()))?;

    match format.as_str() {
        "wav" => {
            if full_data {
                extract_full_audio_data_from_wav(abs_path)
            } else {
                let (_, info) = extract_info_from_wav(abs_path)?;
                let info = info.ok_or_else(|| Error::Io("无法获取WAV音频信息".to_string()))?;
                Ok((vec![0.0], info))
            }
        }
        "mp3" => {
            if full_data {
                extract_full_audio_data_from_mp3(abs_path)
            } else {
                let (_, info) = extract_info_from_mp3(abs_path)?;
                let info = info.ok_or_else(|| Error::Io("无法获取MP3音频信息".to_string()))?;
                Ok((vec![0.0], info))
            }
        }
        "mp4" => {
            if full_data {
                extract_full_audio_data_from_mp4(abs_path)
            } else {
                let (_, info) = extract_info_from_mp4(abs_path)?;
                let info = info.ok_or_else(|| Error::Io("无法获取MP4音频信息".to_string()))?;
                Ok((vec![0.0], info))
            }
        }
        _ => Err(Error::NotFound(format!("暂不支持的音频格式: {}", format))),
    }
}
