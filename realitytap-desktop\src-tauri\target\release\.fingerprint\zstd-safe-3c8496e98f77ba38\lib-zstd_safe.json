{"rustc": 10895048813736897673, "features": "[\"std\"]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"seekable\", \"std\", \"thin\", \"thin-lto\", \"zdict_builder\", \"zstdmt\"]", "target": 13834647262792939399, "profile": 15794574819684997721, "path": 16858754854515904417, "deps": [[8373447648276846408, "zstd_sys", false, 11673213721745867896], [15788444815745660356, "build_script_build", false, 4939870557353014247]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zstd-safe-3c8496e98f77ba38\\dep-lib-zstd_safe", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}