// RealityTap 触觉反馈统一命令模块
//
// 该模块整合了简化架构和统一路由功能，提供完整的触觉反馈命令接口。
// 主要特点：
// - 简化架构为核心实现
// - 统一路由提供兼容性
// - 保持与原有 API 的完全兼容
// - 支持渐进式迁移

use std::collections::HashMap;
use std::sync::{Arc, Mutex};

use std::fs;
use serde::{Deserialize, Serialize};
use once_cell::sync::Lazy;
use tauri::AppHandle;

use crate::haptic::{
    ffi::get_api,
    error::{convert_c_error, get_enhanced_error_info},
    DEFAULT_MOTOR_DRIVE_FREQ, DEFAULT_SAMPLING_RATE, DEFAULT_CONFIG_FILE,
};

// ===== 类型定义 =====

/// 统一的设备配置（兼容所有架构）
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, PartialEq)]
pub struct HapticDeviceConfig {
    pub id: u32,
    pub motor_drive_freq: u32,
    pub config_file: String,
    pub sampling_rate: u32,
    pub buffer_size: Option<u32>,
    pub timeout_ms: Option<u64>,
    pub enable_logging: Option<bool>,
    // 简化架构专用字段
    pub enable_events: Option<bool>,
    pub sample_throttle_ms: Option<u64>,
}

impl Default for HapticDeviceConfig {
    fn default() -> Self {
        Self {
            id: 0,
            motor_drive_freq: DEFAULT_MOTOR_DRIVE_FREQ,
            config_file: DEFAULT_CONFIG_FILE.to_string(),
            sampling_rate: DEFAULT_SAMPLING_RATE,
            buffer_size: None,
            timeout_ms: Some(5000),
            enable_logging: Some(true),
            enable_events: Some(true),
            sample_throttle_ms: Some(10),
        }
    }
}

/// 统一的播放参数（兼容所有架构）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HapticPlayParams {
    pub data: Vec<i32>,
    pub interval_ms: Option<u32>,
    pub loop_count: Option<u32>,
    pub amplitude: Option<i32>,
    pub frequency_offset: Option<i32>,
}

/// 保存到文件的参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HapticSaveParams {
    pub data: Vec<i32>,
    pub file_path: String,
    pub interval_ms: Option<u32>,
    pub loop_count: Option<u32>,
    pub amplitude: Option<i32>,
    pub frequency_offset: Option<i32>,
}





// ===== 简化架构管理器 =====

/// 简化的管理器状态
#[derive(Debug)]
pub(crate) struct SimpleManagerState {
    pub(crate) is_initialized: bool,
    pub(crate) is_started: bool,
    pub(crate) device_configs: HashMap<u32, HapticDeviceConfig>,
    pub(crate) app_handle: Option<AppHandle>,
}

impl Default for SimpleManagerState {
    fn default() -> Self {
        Self {
            is_initialized: false,
            is_started: false,
            device_configs: HashMap::new(),
            app_handle: None,
        }
    }
}

/// 全局简化管理器实例
static SIMPLE_HAPTIC_MANAGER: Lazy<Arc<Mutex<SimpleManagerState>>> = 
    Lazy::new(|| Arc::new(Mutex::new(SimpleManagerState::default())));

impl SimpleManagerState {
    /// 设置应用句柄
    fn set_app_handle(&mut self, app_handle: AppHandle) {
        self.app_handle = Some(app_handle);
    }

    /// 检查配置是否相同
    pub(crate) fn configs_equal(&self, new_configs: &[HapticDeviceConfig]) -> bool {
        if self.device_configs.len() != new_configs.len() {
            return false;
        }

        for config in new_configs {
            if let Some(existing) = self.device_configs.get(&config.id) {
                if existing != config {
                    return false;
                }
            } else {
                return false;
            }
        }
        true
    }

    /// 初始化设备（幂等性设计）
    fn initialize(&mut self, configs: Vec<HapticDeviceConfig>) -> Result<(), String> {
        if self.is_initialized {
            // 检查配置是否相同
            if self.configs_equal(&configs) {
                log::info!("管理器已使用相同配置初始化，操作成功");
                return Ok(());
            } else {
                log::info!("检测到配置变更，执行重新初始化");
                return self.reinitialize(configs);
            }
        }

        // 首次初始化逻辑
        // 清理旧配置
        self.device_configs.clear();

        // 保存配置
        for config in configs.iter() {
            self.device_configs.insert(config.id, config.clone());
            log::info!("设备 {} 配置保存成功", config.id);
        }

        // 实际调用 librtcore 初始化
        self.call_librtcore_init(&configs)?;

        self.is_initialized = true;
        log::info!("管理器初始化完成，设备数量: {}", self.device_configs.len());
        Ok(())
    }

    /// 重新初始化设备（使用 DLL 重新加载）
    ///
    /// 注意：此方法可以在管理器未初始化状态下调用，相当于首次初始化
    fn reinitialize(&mut self, configs: Vec<HapticDeviceConfig>) -> Result<(), String> {
        log::info!("开始重新初始化，当前状态: {}", if self.is_initialized { "已初始化" } else { "未初始化" });

        // 更新配置
        self.device_configs.clear();
        for config in configs.iter() {
            self.device_configs.insert(config.id, config.clone());
            log::info!("设备 {} 配置更新成功", config.id);
        }

        // 实际调用 librtcore 重新初始化（使用 DLL 重新加载）
        self.call_librtcore_dll_reload_reinit(&configs)?;

        // 更新初始化状态
        self.is_initialized = true;
        self.is_started = false; // 重新初始化后需要重新启动

        // 自动启动播放系统
        log::info!("DLL 重新加载重新初始化完成，自动启动播放系统");
        self.start()?;

        log::info!("管理器重新初始化完成，设备数量: {}", self.device_configs.len());
        Ok(())
    }

    /// 调用 librtcore 初始化 API（使用全局单例 Handler）
    fn call_librtcore_init(&self, configs: &[HapticDeviceConfig]) -> Result<(), String> {
        use crate::haptic::ffi::{get_api, load_library};
        use crate::haptic::handler::{
            initialize_global_handler, get_global_handler_c_ptr, update_global_handler_sampling_rate
        };

        // 确保库已加载
        load_library().map_err(|e| format!("加载库失败: {}", e))?;

        // 获取 API
        let api_guard = get_api().map_err(|e| format!("获取API失败: {}", e))?;
        let api = api_guard.lock().unwrap();

        if let Some(ref api) = *api {
            // 初始化全局单例 Handler
            if let Some(app_handle) = &self.app_handle {
                initialize_global_handler(app_handle.clone());
                log::info!("全局单例 Handler 已初始化");
            }

            // 使用第一个设备配置的采样率更新全局 Handler
            if let Some(first_config) = configs.first() {
                let sampling_rate = crate::haptic::ffi::SamplingRateType::from(first_config.sampling_rate);
                update_global_handler_sampling_rate(sampling_rate);
                log::info!("全局 Handler 采样率已设置: {:?}", sampling_rate);
            }

            // 获取全局 Handler 的稳定指针
            let handler_ptr = get_global_handler_c_ptr();
            log::info!("获取到全局 Handler 指针: {:p}", handler_ptr);

            // 转换配置为 C 兼容格式
            // 注意：所有设备配置都使用同一个 handler
            let mut c_params = Vec::new();
            for config in configs {
                let c_param = crate::haptic::ffi::HapticActuatorParams {
                    id: config.id,
                    f0: config.motor_drive_freq,
                    file: std::ffi::CString::new(config.config_file.clone())
                        .map_err(|e| format!("配置文件路径转换失败: {}", e))?
                        .into_raw(),
                    sampling_rate: crate::haptic::ffi::SamplingRateType::from(config.sampling_rate),
                    output_handler: handler_ptr as *mut _,  // 所有配置都使用同一个全局 Handler
                };
                c_params.push(c_param);
                log::info!("设备 {} 配置已添加，使用全局 Handler", config.id);
            }

            // 调用 awa_realitytap_init
            let result = unsafe {
                (api.init)(c_params.as_mut_ptr(), c_params.len() as isize)
            };

            if result != 0 {
                let error_info = get_enhanced_error_info(result);
                log::error!("awa_realitytap_init 失败: {}", error_info.technical_details);
                return Err(format!("初始化失败: {} (错误码: {})", error_info.user_message, result));
            }

            log::info!("awa_realitytap_init 调用成功 (使用全局单例 Handler)");
            Ok(())
        } else {
            Err("API 未加载".to_string())
        }
    }

    /// 调用 librtcore DLL 重新加载重新初始化（使用全局单例 Handler）
    ///
    /// 由于 awa_realitytap_reinit 存在已知缺陷，此方法使用 DLL 完全重新加载的方式
    /// 来实现重新初始化，确保所有内部状态被正确重置。
    fn call_librtcore_dll_reload_reinit(&self, configs: &[HapticDeviceConfig]) -> Result<(), String> {
        use crate::haptic::ffi::{get_api, reload_library_completely};
        use crate::haptic::handler::{
            SimpleHandlerConfig, reconfigure_global_handler, get_global_handler_c_ptr,
            start_waiting_for_callback, stop_waiting_for_callback, wait_for_callback_completion_sync,
            get_global_haptic_handler, update_global_handler_sampling_rate
        };

        // 完全重新加载 DLL（绕过 awa_realitytap_reinit 的缺陷）
        log::info!("开始 DLL 完全重新加载以实现重新初始化");
        reload_library_completely().map_err(|e| format!("DLL 重新加载失败: {}", e))?;

        // 获取重新加载后的 API
        let api_guard = get_api().map_err(|e| format!("获取API失败: {}", e))?;
        let api = api_guard.lock().unwrap();

        if let Some(ref api) = *api {
            // 注意：DLL 重新加载后，不需要调用 append_stop，因为所有状态已重置
            log::info!("DLL 重新加载完成，跳过停止操作和回调等待（状态已重置）");

            // 更新全局 Handler 的采样率
            if let Some(first_config) = configs.first() {
                let sampling_rate = crate::haptic::ffi::SamplingRateType::from(first_config.sampling_rate);
                update_global_handler_sampling_rate(sampling_rate);
                log::info!("DLL 重新加载后更新全局 Handler 采样率: {:?}", sampling_rate);
            } else {
                return Err("至少需要一个设备配置".to_string());
            }

            // 获取全局 Handler 的稳定指针
            let handler_ptr = get_global_handler_c_ptr();
            log::info!("获取到全局 Handler 指针: {:p}", handler_ptr);

            // 转换配置为 C 兼容格式
            // 注意：所有设备配置都使用同一个 handler
            let mut c_params = Vec::new();
            for config in configs {
                let c_param = crate::haptic::ffi::HapticActuatorParams {
                    id: config.id,
                    f0: config.motor_drive_freq,
                    file: std::ffi::CString::new(config.config_file.clone())
                        .map_err(|e| format!("配置文件路径转换失败: {}", e))?
                        .into_raw(),
                    sampling_rate: crate::haptic::ffi::SamplingRateType::from(config.sampling_rate),
                    output_handler: handler_ptr as *mut _,  // 所有配置都使用同一个全局 Handler
                };
                c_params.push(c_param);
                log::info!("设备 {} DLL 重新加载后配置已添加，使用全局 Handler", config.id);
            }

            // 调用 awa_realitytap_init（DLL 重新加载后使用 init 而不是 reinit）
            log::info!("调用 awa_realitytap_init（DLL 重新加载后），设备数量: {}", c_params.len());
            let result = unsafe {
                (api.init)(c_params.as_mut_ptr(), c_params.len() as isize)
            };

            if result != 0 {
                let error_info = get_enhanced_error_info(result);
                log::error!("DLL 重新加载后 awa_realitytap_init 失败: {}", error_info.technical_details);
                return Err(format!("DLL 重新加载重新初始化失败: {} (错误码: {})", error_info.user_message, result));
            }

            log::info!("DLL 重新加载后 awa_realitytap_init 调用成功 (使用全局单例 Handler)");
            Ok(())
        } else {
            Err("API 未加载".to_string())
        }
    }

    /// 启动播放系统
    fn start(&mut self) -> Result<(), String> {
        if !self.is_initialized {
            return Err("请先初始化管理器".to_string());
        }

        if self.is_started {
            return Err("播放系统已启动".to_string());
        }

        // 调用 C++ 启动
        let api_guard = get_api().map_err(|e| e.to_string())?;
        let api = api_guard.lock().unwrap();
        
        if let Some(ref api) = *api {
            let init_result = unsafe { (api.append_init)() };
            if init_result < 0 {
                let error = convert_c_error(init_result);
                return Err(format!("初始化播放系统失败: {}", error));
            }

            let start_result = unsafe { (api.append_start)() };
            if start_result < 0 {
                let error = convert_c_error(start_result);
                return Err(format!("启动播放系统失败: {}", error));
            }
        } else {
            return Err("API 未加载".to_string());
        }

        self.is_started = true;
        log::info!("播放系统启动成功 (简化版)");
        Ok(())
    }

    /// 停止播放系统（使用全局单例 Handler 的安全停止）
    /// 修复：添加更好的同步等待机制
    fn stop(&mut self) -> Result<(), String> {
        if !self.is_started {
            return Ok(()); // 已经停止
        }

        // 使用全局 Handler 的停止功能（同步部分）
        use crate::haptic::handler::{
            get_global_haptic_handler, start_waiting_for_callback,
            stop_waiting_for_callback, cleanup_pending_unregister_handlers,
            wait_for_callback_completion_sync
        };

        log::info!("开始停止播放系统");

        // 1. 开始等待回调完成
        start_waiting_for_callback();

        // 2. 调用停止
        {
            let handler = get_global_haptic_handler().lock().unwrap();
            handler.safe_stop()?;
        }

        // 3. 同步等待回调完成
        let callback_completed = wait_for_callback_completion_sync(500); // 等待最多500ms
        if !callback_completed {
            log::warn!("等待停止回调完成超时");
        }

        // 4. 停止等待
        stop_waiting_for_callback();

        // 5. 清理待注销的handler
        cleanup_pending_unregister_handlers();

        self.is_started = false;
        log::info!("播放系统停止成功 (使用全局单例 Handler)");
        Ok(())
    }

    /// 播放触觉数据
    fn play(&self, params: HapticPlayParams) -> Result<(), String> {
        if !self.is_initialized {
            return Err("请先初始化管理器".to_string());
        }

        if !self.is_started {
            return Err("请先启动播放系统".to_string());
        }

        // 直接使用全局 Handler 进行播放（始终为事件模式）
        let api_guard = get_api().map_err(|e| e.to_string())?;
        let api = api_guard.lock().unwrap();

        if let Some(ref api) = *api {
            let result = unsafe {
                (api.append_haptics)(
                    params.data.as_ptr(),
                    params.data.len() as u32,
                    params.interval_ms.unwrap_or(0),
                    params.loop_count.unwrap_or(1),
                    params.amplitude.unwrap_or(255),
                    params.frequency_offset.unwrap_or(0),
                )
            };

            if result < 0 {
                let error = convert_c_error(result);
                return Err(format!("播放失败: {}", error));
            }
        } else {
            return Err("API 未加载".to_string());
        }

        log::info!("触觉数据播放成功");
        Ok(())
    }

    /// 保存触觉数据到文件
    fn save_to_file(&mut self, params: HapticSaveParams) -> Result<(), String> {
        if !self.is_initialized {
            return Err("请先初始化管理器".to_string());
        }

        if !self.is_started {
            return Err("请先启动播放系统".to_string());
        }

        // 临时切换全局 Handler 到文件模式
        use crate::haptic::handler::switch_global_handler_to_file_mode;

        // 切换到文件模式（会自动保存原始配置）
        switch_global_handler_to_file_mode(params.file_path.clone())?;

        // 调用播放 API，Handler 会将数据保存到文件
        let api_guard = get_api().map_err(|e| e.to_string())?;
        let api = api_guard.lock().unwrap();

        let result = if let Some(ref api) = *api {
            unsafe {
                (api.append_haptics)(
                    params.data.as_ptr(),
                    params.data.len() as u32,
                    params.interval_ms.unwrap_or(0),
                    params.loop_count.unwrap_or(1),
                    params.amplitude.unwrap_or(255),
                    params.frequency_offset.unwrap_or(0),
                )
            }
        } else {
            return Err("API 未加载".to_string());
        };

        if result < 0 {
            let error = convert_c_error(result);
            return Err(format!("保存到文件失败: {}", error));
        }

        log::info!("触觉数据保存到文件操作已启动: {}", params.file_path);

        // 注意：配置会在 Handler 的 onHapticOutputComplete 或 onHapticOutputStop 中自动恢复

        Ok(())
    }

    /// 设置振幅
    fn set_amplitude(&self, amplitude: u32) -> Result<(), String> {
        if !self.is_initialized {
            return Err("请先初始化管理器".to_string());
        }

        let api_guard = get_api().map_err(|e| e.to_string())?;
        let api = api_guard.lock().unwrap();
        
        if let Some(ref api) = *api {
            let result = unsafe {
                (api.set_amplitude)(amplitude)
            };

            if result != 0 {
                let error_info = get_enhanced_error_info(result);
                log::error!("设置振幅失败: {}", error_info.technical_details);
                return Err(format!("设置振幅失败: {} (错误码: {})", error_info.user_message, result));
            }
        } else {
            return Err("API 未加载".to_string());
        }

        log::info!("振幅设置成功 (简化版): {}", amplitude);
        Ok(())
    }

    /// 清理资源（使用全局单例 Handler 的安全停止）
    /// 修复：添加更完善的清理机制
    fn cleanup(&mut self) -> Result<(), String> {
        use crate::haptic::handler::cleanup_pending_unregister_handlers;

        log::info!("开始清理简化管理器资源");

        // 1. 停止播放系统
        if self.is_started {
            log::info!("停止播放系统");
            let _ = self.stop();
        }

        // 2. 等待一段时间确保所有操作完成
        std::thread::sleep(std::time::Duration::from_millis(300));

        // 3. 清理待注销的handler
        cleanup_pending_unregister_handlers();

        // 4. 清理配置
        self.device_configs.clear();

        self.is_initialized = false;
        log::info!("简化管理器清理完成 (全局 Handler 保持存在)");
        Ok(())
    }

    /// 获取状态信息
    fn get_status(&self) -> serde_json::Value {
        serde_json::json!({
            "is_initialized": self.is_initialized,
            "is_started": self.is_started,
            "device_count": self.device_configs.len(),
            "version": "simplified"
        })
    }
}

// ===== Tauri 命令函数 =====





// ----- 触觉反馈命令 -----

/// 初始化触觉反馈库
#[tauri::command]
pub async fn haptic_init_unified(
    app_handle: AppHandle,
    configs: Vec<HapticDeviceConfig>
) -> Result<String, String> {
    log::info!("开始初始化触觉反馈库，设备数量: {}", configs.len());

    let mut manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    manager.set_app_handle(app_handle);
    manager.initialize(configs)?;
    Ok("触觉反馈库初始化成功".to_string())
}

/// 启动触觉播放系统
#[tauri::command]
pub async fn haptic_start_unified() -> Result<String, String> {
    log::info!("启动触觉播放系统");

    let mut manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    manager.start()?;
    Ok("触觉播放系统启动成功".to_string())
}

/// 停止触觉播放系统
#[tauri::command]
pub async fn haptic_stop_unified() -> Result<String, String> {
    log::info!("停止触觉播放系统");

    // 同步停止，然后异步等待回调完成
    {
        let mut manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
        manager.stop()?;
    }

    // 异步等待回调完成
    use crate::haptic::handler::{wait_for_callback_completion, stop_waiting_for_callback};
    let completed = wait_for_callback_completion(1000).await;
    if !completed {
        log::warn!("等待回调完成超时");
    }
    stop_waiting_for_callback();

    Ok("触觉播放系统停止成功".to_string())
}

/// 播放触觉数据
#[tauri::command]
pub async fn haptic_play_unified(params: HapticPlayParams) -> Result<String, String> {
    log::debug!("播放触觉数据，数据长度: {}", params.data.len());

    let manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    manager.play(params)?;
    Ok("触觉数据播放成功".to_string())
}

/// 保存触觉数据到文件
#[tauri::command]
pub async fn haptic_save_to_file(params: HapticSaveParams) -> Result<String, String> {
    log::debug!("保存触觉数据到文件，数据长度: {}, 文件路径: {}", params.data.len(), params.file_path);

    let mut manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    manager.save_to_file(params)?;
    Ok("触觉数据保存到文件成功".to_string())
}

/// 设置振幅
#[tauri::command]
pub async fn haptic_set_amplitude_unified(amplitude: u32) -> Result<String, String> {
    log::debug!("设置振幅: {}", amplitude);

    let manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    manager.set_amplitude(amplitude)?;
    Ok("振幅设置成功".to_string())
}

/// 获取状态
#[tauri::command]
pub async fn haptic_get_status_unified() -> Result<serde_json::Value, String> {
    let manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    Ok(manager.get_status())
}



/// 清理资源
#[tauri::command]
pub async fn haptic_cleanup_unified() -> Result<String, String> {
    log::info!("清理触觉反馈资源");

    // 同步清理，然后异步等待回调完成
    {
        let mut manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
        manager.cleanup()?;
    }

    // 异步等待回调完成
    use crate::haptic::handler::{wait_for_callback_completion, stop_waiting_for_callback};
    let completed = wait_for_callback_completion(1000).await;
    if !completed {
        log::warn!("等待回调完成超时");
    }
    stop_waiting_for_callback();

    Ok("资源清理成功".to_string())
}

/// 重新初始化触觉反馈库
///
/// 由于 awa_realitytap_reinit 存在已知缺陷，此函数使用 DLL 完全重新加载的方式
/// 来实现重新初始化，确保马达型号和采样率变更能够正确生效。
#[tauri::command]
pub async fn haptic_reinit_unified(
    app_handle: AppHandle,
    configs: Vec<HapticDeviceConfig>
) -> Result<String, String> {
    log::info!("重新初始化触觉反馈库，使用 DLL 重新加载机制（绕过有缺陷的 awa_realitytap_reinit）");

    // 尝试使用 DLL 重新加载重新初始化
    let reinit_result = {
        let mut manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
        manager.set_app_handle(app_handle.clone());
        manager.reinitialize(configs.clone())
    }; // 锁在这里被释放

    match reinit_result {
        Ok(()) => {
            log::info!("使用 DLL 重新加载重新初始化成功");
            Ok("触觉反馈库重新初始化成功".to_string())
        }
        Err(e) => {
            log::warn!("DLL 重新加载重新初始化失败，回退到清理+重新初始化模式: {}", e);

            // 回退方案：先清理
            let _ = haptic_cleanup_unified().await;

            // 等待清理完成
            tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

            // 重新初始化
            haptic_init_unified(app_handle, configs).await
        }
    }
}

/// 获取架构信息
#[tauri::command]
pub async fn haptic_get_architecture_info() -> Result<serde_json::Value, String> {
    Ok(serde_json::json!({
        "architecture": "simplified",
        "simplified_enabled": true,
        "version": "unified-v2.0",
        "description": "统一架构，简化实现"
    }))
}

/// 获取设备状态（兼容性）
#[tauri::command]
pub async fn haptic_get_device_status(device_id: u32) -> Result<serde_json::Value, String> {
    let manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    let status = manager.get_status();

    Ok(serde_json::json!({
        "device_id": device_id,
        "is_initialized": status["is_initialized"],
        "is_started": status["is_started"],
        "version": "simplified"
    }))
}

/// 获取所有设备状态（兼容性）
#[tauri::command]
pub async fn haptic_get_all_device_status() -> Result<serde_json::Value, String> {
    let manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    let status = manager.get_status();

    Ok(serde_json::json!({
        "devices": [status],
        "total_count": status["device_count"],
        "version": "simplified"
    }))
}

/// 获取库状态（兼容性）
#[tauri::command]
pub async fn haptic_get_library_status() -> Result<serde_json::Value, String> {
    let manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    Ok(manager.get_status())
}

/// 获取可用配置（从可执行文件同目录下的 motors/ 目录读取）
#[tauri::command]
pub async fn haptic_get_available_configs() -> Result<Vec<String>, String> {
    // 获取可执行文件所在目录
    let exe_path = std::env::current_exe()
        .map_err(|e| format!("无法获取可执行文件路径: {}", e))?;

    let exe_dir = exe_path.parent()
        .ok_or("无法获取可执行文件目录")?;

    // 构建 motors 目录路径
    let motors_dir = exe_dir.join("motors");

    // 检查 motors 目录是否存在
    if !motors_dir.exists() {
        log::warn!("motors 目录不存在: {:?}", motors_dir);
        return Ok(vec![]);
    }

    // 读取目录中的配置文件
    let mut config_files = Vec::new();

    match fs::read_dir(&motors_dir) {
        Ok(entries) => {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();
                    if path.is_file() {
                        if let Some(file_name) = path.file_name() {
                            if let Some(file_name_str) = file_name.to_str() {
                                // 只包含 .conf 和 .bin 文件
                                if file_name_str.ends_with(".conf") || file_name_str.ends_with(".bin") {
                                    // 返回相对于可执行文件的路径
                                    let relative_path = format!("motors/{}", file_name_str);
                                    config_files.push(relative_path);
                                }
                            }
                        }
                    }
                }
            }
        }
        Err(e) => {
            log::error!("读取 motors 目录失败: {}", e);
            return Err(format!("读取 motors 目录失败: {}", e));
        }
    }

    // 按文件名排序
    config_files.sort();

    log::info!("找到 {} 个马达配置文件: {:?}", config_files.len(), config_files);

    Ok(config_files)
}

/// 验证配置文件（检查文件是否实际存在）
#[tauri::command]
pub async fn haptic_validate_config_file(config_file: String) -> Result<bool, String> {
    // 检查文件名格式
    if !config_file.contains("motors/") ||
       (!config_file.ends_with(".conf") && !config_file.ends_with(".bin")) {
        return Ok(false);
    }

    // 获取可执行文件所在目录
    let exe_path = std::env::current_exe()
        .map_err(|e| format!("无法获取可执行文件路径: {}", e))?;

    let exe_dir = exe_path.parent()
        .ok_or("无法获取可执行文件目录")?;

    // 构建完整的文件路径
    let full_path = exe_dir.join(&config_file);

    // 检查文件是否存在
    Ok(full_path.exists() && full_path.is_file())
}

/// 获取配置文件信息（读取实际文件信息）
#[tauri::command]
pub async fn haptic_get_config_file_info(config_file: String) -> Result<serde_json::Value, String> {
    // 从文件路径提取文件名
    let file_name = config_file.split('/').last().unwrap_or(&config_file).to_string();

    // 获取可执行文件所在目录
    let exe_path = std::env::current_exe()
        .map_err(|e| format!("无法获取可执行文件路径: {}", e))?;

    let exe_dir = exe_path.parent()
        .ok_or("无法获取可执行文件目录")?;

    // 构建完整的文件路径
    let full_path = exe_dir.join(&config_file);

    // 检查文件是否存在并获取文件信息
    let (exists, size, is_valid) = if full_path.exists() && full_path.is_file() {
        match fs::metadata(&full_path) {
            Ok(metadata) => {
                let size = metadata.len();
                let is_valid = config_file.ends_with(".conf") || config_file.ends_with(".bin");
                (true, size, is_valid)
            }
            Err(e) => {
                log::warn!("无法读取文件元数据 {}: {}", config_file, e);
                (false, 0, false)
            }
        }
    } else {
        (false, 0, false)
    };

    let description = if exists {
        format!("马达配置文件: {} ({:.1} KB)", file_name, size as f64 / 1024.0)
    } else {
        format!("配置文件不存在: {}", file_name)
    };

    Ok(serde_json::json!({
        "path": config_file,
        "name": file_name,
        "size": size,
        "exists": exists,
        "is_valid": is_valid,
        "description": description,
        "version": "file_system"
    }))
}

/// 获取所有配置信息（兼容性）
#[tauri::command]
pub async fn haptic_get_all_config_info() -> Result<Vec<serde_json::Value>, String> {
    let configs = haptic_get_available_configs().await?;
    let mut config_infos = Vec::new();

    for config in configs {
        let info = haptic_get_config_file_info(config).await?;
        config_infos.push(info);
    }

    // 直接返回配置信息数组，而不是包装在对象中
    Ok(config_infos)
}

/// 强制重置（兼容性）
#[tauri::command]
pub async fn haptic_force_reset() -> Result<String, String> {
    log::info!("强制重置触觉反馈系统");
    haptic_cleanup_unified().await
}

/// 获取生命周期状态（兼容性）
#[tauri::command]
pub async fn haptic_get_lifecycle_status() -> Result<serde_json::Value, String> {
    let manager = SIMPLE_HAPTIC_MANAGER.lock().unwrap();
    let status = manager.get_status();

    Ok(serde_json::json!({
        "lifecycle_state": if status["is_initialized"].as_bool().unwrap_or(false) {
            "initialized"
        } else {
            "not_initialized"
        },
        "is_running": status["is_started"],
        "version": "simplified"
    }))
}

/// 测试DLL加载路径（调试用）
#[cfg(debug_assertions)]
#[tauri::command]
pub async fn haptic_test_dll_paths() -> Result<Vec<(String, bool)>, String> {
    use crate::haptic::ffi::test_dll_loading_paths;

    log::info!("开始测试DLL加载路径");
    let results = test_dll_loading_paths();

    for (path, success) in &results {
        if *success {
            log::info!("✓ {}", path);
        } else {
            log::warn!("✗ {}", path);
        }
    }

    Ok(results)
}
