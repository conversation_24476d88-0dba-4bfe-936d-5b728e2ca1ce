{"rustc": 10895048813736897673, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 2040997289075261528, "path": 7686746791315936583, "deps": [[5491919304041016563, "build_script_build", false, 3280121140819753598], [8995469080876806959, "untrusted", false, 8455846756910104795], [9920160576179037441, "getrandom", false, 11221399134735489991], [10411997081178400487, "cfg_if", false, 9212329857759087126]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ring-bb804e764e9d86bc\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}