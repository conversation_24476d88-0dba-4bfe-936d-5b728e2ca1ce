{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 3284814487306948542, "deps": [[40386456601120721, "percent_encoding", false, 11430224217469457874], [442785307232013896, "tauri_runtime", false, 18422432015645049330], [1200537532907108615, "url<PERSON><PERSON>n", false, 17867119224593606479], [3150220818285335163, "url", false, 5408171043244425519], [4143744114649553716, "raw_window_handle", false, 8959467065406847557], [4341921533227644514, "muda", false, 3671206635572034943], [4919829919303820331, "serialize_to_javascript", false, 17428035850388715376], [5138218615291878843, "tokio", false, 15250874349822087297], [5986029879202738730, "log", false, 15354736063082002461], [7752760652095876438, "tauri_runtime_wry", false, 5220071502683921407], [8539587424388551196, "webview2_com", false, 8220371095569886386], [9010263965687315507, "http", false, 16950085000799907330], [9228235415475680086, "tauri_macros", false, 12229972015381553744], [9689903380558560274, "serde", false, 3282804803960374299], [9920160576179037441, "getrandom", false, 3745091267186805900], [10229185211513642314, "mime", false, 17650519331291606697], [10629569228670356391, "futures_util", false, 757868430050288205], [10755362358622467486, "build_script_build", false, 14327971614320458018], [10806645703491011684, "thiserror", false, 9617457741875845117], [11050281405049894993, "tauri_utils", false, 4988308089169363450], [11989259058781683633, "dunce", false, 10951288274889859025], [12565293087094287914, "window_vibrancy", false, 2968740957300589537], [12986574360607194341, "serde_repr", false, 2166301678314986041], [13077543566650298139, "heck", false, 7607099312003041627], [13116089016666501665, "windows", false, 12387249425082537729], [13625485746686963219, "anyhow", false, 9447231670975916980], [15367738274754116744, "serde_json", false, 15916909028635074265], [16928111194414003569, "dirs", false, 2924358837788341285], [17155886227862585100, "glob", false, 11869800844943428801]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-cdeea4bd217449cc\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}