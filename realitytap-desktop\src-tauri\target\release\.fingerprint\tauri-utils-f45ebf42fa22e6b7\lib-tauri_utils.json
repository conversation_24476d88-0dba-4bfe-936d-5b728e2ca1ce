{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2040997289075261528, "path": 12241120027172578749, "deps": [[561782849581144631, "html5ever", false, 17148842850588506753], [1200537532907108615, "url<PERSON><PERSON>n", false, 7367018399078521516], [3129130049864710036, "memchr", false, 12136722506247284342], [3150220818285335163, "url", false, 7399539242944503594], [3191507132440681679, "serde_untagged", false, 8499321896947598870], [4899080583175475170, "semver", false, 1014852567012646525], [5986029879202738730, "log", false, 4278780275571731749], [6213549728662707793, "serde_with", false, 3036723398846896609], [6262254372177975231, "kuchiki", false, 1693810335705285106], [6606131838865521726, "ctor", false, 9413999893018703200], [7170110829644101142, "json_patch", false, 5098650908747837720], [8269115081296425610, "uuid", false, 4957334133351173316], [8786711029710048183, "toml", false, 14629175704145273794], [9010263965687315507, "http", false, 537038018071154330], [9451456094439810778, "regex", false, 13827262411219907142], [9689903380558560274, "serde", false, 12791218709341986024], [10806645703491011684, "thiserror", false, 13729077771554084640], [11989259058781683633, "dunce", false, 6127914047667850541], [13625485746686963219, "anyhow", false, 274638999200539270], [14132538657330703225, "brotli", false, 2367305165013279337], [15367738274754116744, "serde_json", false, 7458677227193029867], [15622660310229662834, "walkdir", false, 4672370185453201645], [17146114186171651583, "infer", false, 3533842611247403007], [17155886227862585100, "glob", false, 3585983256673177101], [17186037756130803222, "phf", false, 1603171652695985635]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-utils-f45ebf42fa22e6b7\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}