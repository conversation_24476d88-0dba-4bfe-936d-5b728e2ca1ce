{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2040997289075261528, "path": 3349273884199032985, "deps": [[376837177317575824, "softbuffer", false, 4476743909122222203], [442785307232013896, "tauri_runtime", false, 3726187632709565753], [3150220818285335163, "url", false, 7399539242944503594], [3722963349756955755, "once_cell", false, 1740554149852721105], [4143744114649553716, "raw_window_handle", false, 826242152692617864], [5986029879202738730, "log", false, 4278780275571731749], [7752760652095876438, "build_script_build", false, 15462390429588191530], [8539587424388551196, "webview2_com", false, 107903819203652563], [9010263965687315507, "http", false, 537038018071154330], [11050281405049894993, "tauri_utils", false, 16407344261864543779], [13116089016666501665, "windows", false, 16525752076480673242], [13223659721939363523, "tao", false, 8928579645527255139], [14794439852947137341, "wry", false, 13472077837746563776]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-runtime-wry-51a76c5065aaf941\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}