{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"arrays\", \"bindgen\", \"debug\", \"default\", \"doc-cfg\", \"experimental\", \"fat-lto\", \"legacy\", \"no_asm\", \"pkg-config\", \"thin\", \"thin-lto\", \"wasm\", \"zdict_builder\", \"zstdmt\"]", "target": 13967053409313941148, "profile": 2040997289075261528, "path": 7317753324574137790, "deps": [[15788444815745660356, "zstd_safe", false, 5394742679082714661]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\zstd-6f8f6aaadc5f369c\\dep-lib-zstd", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}