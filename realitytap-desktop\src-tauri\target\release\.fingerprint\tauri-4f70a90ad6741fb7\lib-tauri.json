{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2040997289075261528, "path": 3284814487306948542, "deps": [[40386456601120721, "percent_encoding", false, 3951988375193713105], [442785307232013896, "tauri_runtime", false, 3726187632709565753], [1200537532907108615, "url<PERSON><PERSON>n", false, 7367018399078521516], [3150220818285335163, "url", false, 7399539242944503594], [4143744114649553716, "raw_window_handle", false, 826242152692617864], [4341921533227644514, "muda", false, 16463684664488365787], [4919829919303820331, "serialize_to_javascript", false, 1631412340139035369], [5138218615291878843, "tokio", false, 5553092168895511418], [5986029879202738730, "log", false, 4278780275571731749], [7752760652095876438, "tauri_runtime_wry", false, 16269037929456011841], [8539587424388551196, "webview2_com", false, 107903819203652563], [9010263965687315507, "http", false, 537038018071154330], [9228235415475680086, "tauri_macros", false, 13584921149266243459], [9689903380558560274, "serde", false, 12791218709341986024], [9920160576179037441, "getrandom", false, 11221399134735489991], [10229185211513642314, "mime", false, 10110181194356012514], [10629569228670356391, "futures_util", false, 5257715238175023452], [10755362358622467486, "build_script_build", false, 2833051112272060660], [10806645703491011684, "thiserror", false, 13729077771554084640], [11050281405049894993, "tauri_utils", false, 16407344261864543779], [11989259058781683633, "dunce", false, 6127914047667850541], [12565293087094287914, "window_vibrancy", false, 7272652236669780382], [12986574360607194341, "serde_repr", false, 12945835134709575597], [13077543566650298139, "heck", false, 14765522513141802040], [13116089016666501665, "windows", false, 16525752076480673242], [13625485746686963219, "anyhow", false, 274638999200539270], [15367738274754116744, "serde_json", false, 7458677227193029867], [16928111194414003569, "dirs", false, 7419600057685510161], [17155886227862585100, "glob", false, 3585983256673177101]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-4f70a90ad6741fb7\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}