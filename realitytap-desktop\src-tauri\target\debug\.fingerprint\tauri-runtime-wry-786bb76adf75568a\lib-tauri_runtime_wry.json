{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 2241668132362809309, "path": 3349273884199032985, "deps": [[376837177317575824, "softbuffer", false, 17863615019613958074], [442785307232013896, "tauri_runtime", false, 1793091974935863447], [3150220818285335163, "url", false, 14863451263885477915], [3722963349756955755, "once_cell", false, 15816061107190650228], [4143744114649553716, "raw_window_handle", false, 3150691046013205847], [5986029879202738730, "log", false, 9664436659979733400], [7752760652095876438, "build_script_build", false, 9286779516598144398], [8539587424388551196, "webview2_com", false, 7462966539188040862], [9010263965687315507, "http", false, 2459037600811677808], [11050281405049894993, "tauri_utils", false, 5215172051886328680], [13116089016666501665, "windows", false, 1979446102959461390], [13223659721939363523, "tao", false, 16374132306082496593], [14794439852947137341, "wry", false, 1537538960790915836]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-786bb76adf75568a\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}