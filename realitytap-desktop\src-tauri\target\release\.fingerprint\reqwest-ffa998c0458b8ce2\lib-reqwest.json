{"rustc": 10895048813736897673, "features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"charset\", \"default\", \"default-tls\", \"h2\", \"http2\", \"json\", \"macos-system-configuration\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"stream\"]", "declared_features": "[\"__rustls\", \"__rustls-ring\", \"__tls\", \"blocking\", \"brotli\", \"charset\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"gzip\", \"h2\", \"hickory-dns\", \"http2\", \"http3\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-manual-roots-no-provider\", \"rustls-tls-native-roots\", \"rustls-tls-native-roots-no-provider\", \"rustls-tls-no-provider\", \"rustls-tls-webpki-roots\", \"rustls-tls-webpki-roots-no-provider\", \"socks\", \"stream\", \"trust-dns\", \"zstd\"]", "target": 8885864859914201979, "profile": 7859547470675518382, "path": 12657710285491601278, "deps": [[40386456601120721, "percent_encoding", false, 3951988375193713105], [95042085696191081, "ipnet", false, 7551102138810064665], [418947936956741439, "h2", false, 12901655279891606358], [784494742817713399, "tower_service", false, 6036151208506612056], [1288403060204016458, "tokio_util", false, 13826514802279172215], [1906322745568073236, "pin_project_lite", false, 8294782031490875826], [2517136641825875337, "sync_wrapper", false, 2705723476857536424], [2883436298747778685, "rustls_pki_types", false, 10724435711602842617], [3150220818285335163, "url", false, 7399539242944503594], [3722963349756955755, "once_cell", false, 1740554149852721105], [5138218615291878843, "tokio", false, 5553092168895511418], [5695049318159433696, "tower", false, 4532143705998521934], [5986029879202738730, "log", false, 4278780275571731749], [7161480121686072451, "rustls", false, 11627926985648990647], [7620660491849607393, "futures_core", false, 9961336037717155709], [8156804143951879168, "webpki_roots", false, 11042770707395995253], [9010263965687315507, "http", false, 537038018071154330], [9689903380558560274, "serde", false, 12791218709341986024], [10229185211513642314, "mime", false, 10110181194356012514], [10595802073777078462, "hyper_util", false, 13452558322726807349], [10629569228670356391, "futures_util", false, 5257715238175023452], [11895591994124935963, "tokio_rustls", false, 17712020327643199050], [11957360342995674422, "hyper", false, 3675683039769659276], [12186126227181294540, "tokio_native_tls", false, 8887202254327881324], [13077212702700853852, "base64", false, 1017511310464611379], [13330729881774643389, "hyper_rustls", false, 15825040625744003334], [14084095096285906100, "http_body", false, 5505652597114369582], [14564311161534545801, "encoding_rs", false, 15221495842737274352], [15032952994102373905, "rustls_pemfile", false, 12233172577374234576], [15367738274754116744, "serde_json", false, 7458677227193029867], [15697835491348449269, "windows_registry", false, 10913965787548900973], [16066129441945555748, "bytes", false, 11085498249730270451], [16542808166767769916, "serde_urlencoded", false, 6583083868722366809], [16785601910559813697, "native_tls_crate", false, 10778373896445000068], [16900715236047033623, "http_body_util", false, 12862087460044794358], [18273243456331255970, "hyper_tls", false, 704608246169224271]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\reqwest-ffa998c0458b8ce2\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}