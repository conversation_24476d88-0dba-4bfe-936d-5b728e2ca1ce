{"$message_type":"diagnostic","message":"unused imports: `SimpleHandlerConfig`, `get_global_haptic_handler`, `reconfigure_global_handler`, `start_waiting_for_callback`, `stop_waiting_for_callback`, and `wait_for_callback_completion_sync`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\haptic\\commands.rs","byte_start":9726,"byte_end":9745,"line_start":265,"line_end":265,"column_start":13,"column_end":32,"is_primary":true,"text":[{"text":"            SimpleHandlerConfig, reconfigure_global_handler, get_global_handler_c_ptr,","highlight_start":13,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\haptic\\commands.rs","byte_start":9747,"byte_end":9773,"line_start":265,"line_end":265,"column_start":34,"column_end":60,"is_primary":true,"text":[{"text":"            SimpleHandlerConfig, reconfigure_global_handler, get_global_handler_c_ptr,","highlight_start":34,"highlight_end":60}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\haptic\\commands.rs","byte_start":9814,"byte_end":9840,"line_start":266,"line_end":266,"column_start":13,"column_end":39,"is_primary":true,"text":[{"text":"            start_waiting_for_callback, stop_waiting_for_callback, wait_for_callback_completion_sync,","highlight_start":13,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\haptic\\commands.rs","byte_start":9842,"byte_end":9867,"line_start":266,"line_end":266,"column_start":41,"column_end":66,"is_primary":true,"text":[{"text":"            start_waiting_for_callback, stop_waiting_for_callback, wait_for_callback_completion_sync,","highlight_start":41,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\haptic\\commands.rs","byte_start":9869,"byte_end":9902,"line_start":266,"line_end":266,"column_start":68,"column_end":101,"is_primary":true,"text":[{"text":"            start_waiting_for_callback, stop_waiting_for_callback, wait_for_callback_completion_sync,","highlight_start":68,"highlight_end":101}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\haptic\\commands.rs","byte_start":9917,"byte_end":9942,"line_start":267,"line_end":267,"column_start":13,"column_end":38,"is_primary":true,"text":[{"text":"            get_global_haptic_handler, update_global_handler_sampling_rate","highlight_start":13,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\haptic\\commands.rs","byte_start":9726,"byte_end":9775,"line_start":265,"line_end":265,"column_start":13,"column_end":62,"is_primary":true,"text":[{"text":"            SimpleHandlerConfig, reconfigure_global_handler, get_global_handler_c_ptr,","highlight_start":13,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src\\haptic\\commands.rs","byte_start":9799,"byte_end":9942,"line_start":265,"line_end":267,"column_start":86,"column_end":38,"is_primary":true,"text":[{"text":"            SimpleHandlerConfig, reconfigure_global_handler, get_global_handler_c_ptr,","highlight_start":86,"highlight_end":87},{"text":"            start_waiting_for_callback, stop_waiting_for_callback, wait_for_callback_completion_sync,","highlight_start":1,"highlight_end":102},{"text":"            get_global_haptic_handler, update_global_handler_sampling_rate","highlight_start":1,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `SimpleHandlerConfig`, `get_global_haptic_handler`, `reconfigure_global_handler`, `start_waiting_for_callback`, `stop_waiting_for_callback`, and `wait_for_callback_completion_sync`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\haptic\\commands.rs:265:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            SimpleHandlerConfig, reconfigure_global_handler, get_global_handler_c_ptr,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            start_waiting_for_callback, stop_waiting_for_callback, wait_for_callback_completion_sync,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m267\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            get_global_haptic_handler, update_global_handler_sampling_rate\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"value assigned to `file_to_remove_path` is never read","code":{"code":"unused_assignments","explanation":null},"level":"warning","spans":[{"file_name":"src\\commands\\file.rs","byte_start":3750,"byte_end":3769,"line_start":122,"line_end":122,"column_start":13,"column_end":32,"is_primary":true,"text":[{"text":"    let mut file_to_remove_path: Option<PathBuf> = None;","highlight_start":13,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"maybe it is overwritten before being read?","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(unused_assignments)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: value assigned to `file_to_remove_path` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\file.rs:122:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let mut file_to_remove_path: Option<PathBuf> = None;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mhelp\u001b[0m\u001b[0m: maybe it is overwritten before being read?\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_assignments)]` on by default\u001b[0m\n\n"}
