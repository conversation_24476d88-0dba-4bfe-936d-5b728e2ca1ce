{"rustc": 10895048813736897673, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 1369601567987815722, "path": 6141386191190606380, "deps": [[4899080583175475170, "semver", false, 12183985207572930630], [6913375703034175521, "schemars", false, 981247787966908288], [7170110829644101142, "json_patch", false, 2064230335387406586], [8786711029710048183, "toml", false, 1712342395853086187], [9689903380558560274, "serde", false, 12432392799593878276], [11050281405049894993, "tauri_utils", false, 6165033782102807894], [12714016054753183456, "tauri_winres", false, 15747755895504210852], [13077543566650298139, "heck", false, 11309666017209925535], [13475171727366188400, "cargo_toml", false, 2975619419551553312], [13625485746686963219, "anyhow", false, 7435008003544188016], [15367738274754116744, "serde_json", false, 7653967626044807545], [15622660310229662834, "walkdir", false, 1977710270525059799], [16928111194414003569, "dirs", false, 18156031852989623030], [17155886227862585100, "glob", false, 15036848553281000914]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-build-cff6d65297b20789\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}