{"rustc": 10895048813736897673, "features": "[\"default\", \"derive\", \"indexmap\", \"preserve_order\", \"schemars_derive\", \"url\", \"uuid1\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 1369601567987815722, "path": 5624443327162653625, "deps": [[3150220818285335163, "url", false, 8003661020387016638], [6913375703034175521, "build_script_build", false, 13076925257459477034], [8269115081296425610, "uuid1", false, 9213008617362214269], [9122563107207267705, "dyn_clone", false, 10248428975372643596], [9689903380558560274, "serde", false, 12432392799593878276], [14923790796823607459, "indexmap", false, 11126831132798158911], [15367738274754116744, "serde_json", false, 7653967626044807545], [16071897500792579091, "schemars_derive", false, 16925552405788439468]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\schemars-7c76ea234dd3e67c\\dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}