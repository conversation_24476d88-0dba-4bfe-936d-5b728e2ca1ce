{"rustc": 10895048813736897673, "features": "[\"with-alloc\"]", "declared_features": "[\"alloc\", \"block-boundary\", \"compiler_builtins\", \"core\", \"default\", \"rustc-dep-of-std\", \"serde\", \"simd\", \"simd-adler32\", \"std\", \"with-alloc\"]", "target": 8661567070972402511, "profile": 5627820096486484124, "path": 18248661613005147823, "deps": [[15407850927583745935, "adler2", false, 11946828238033714424]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\miniz_oxide-41c2de477ff24529\\dep-lib-miniz_oxide", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}