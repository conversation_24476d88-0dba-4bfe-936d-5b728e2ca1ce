{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 2241668132362809309, "path": 3284814487306948542, "deps": [[40386456601120721, "percent_encoding", false, 15614515583053755276], [442785307232013896, "tauri_runtime", false, 1793091974935863447], [1200537532907108615, "url<PERSON><PERSON>n", false, 2266398833021396916], [3150220818285335163, "url", false, 14863451263885477915], [4143744114649553716, "raw_window_handle", false, 3150691046013205847], [4341921533227644514, "muda", false, 16928677577817984416], [4919829919303820331, "serialize_to_javascript", false, 18285644110342620405], [5138218615291878843, "tokio", false, 16490688338640410539], [5986029879202738730, "log", false, 9664436659979733400], [7752760652095876438, "tauri_runtime_wry", false, 4670490161522618010], [8539587424388551196, "webview2_com", false, 7462966539188040862], [9010263965687315507, "http", false, 2459037600811677808], [9228235415475680086, "tauri_macros", false, 11797510506407465998], [9689903380558560274, "serde", false, 18192956005055837394], [9920160576179037441, "getrandom", false, 1569630715321028769], [10229185211513642314, "mime", false, 12440198468503967203], [10629569228670356391, "futures_util", false, 7288157923776882301], [10755362358622467486, "build_script_build", false, 4434827960730790436], [10806645703491011684, "thiserror", false, 6725964427889589046], [11050281405049894993, "tauri_utils", false, 5215172051886328680], [11989259058781683633, "dunce", false, 208018227862425557], [12565293087094287914, "window_vibrancy", false, 3810460907516458906], [12986574360607194341, "serde_repr", false, 2166301678314986041], [13077543566650298139, "heck", false, 12710841905811586498], [13116089016666501665, "windows", false, 1979446102959461390], [13625485746686963219, "anyhow", false, 16547241907567682450], [15367738274754116744, "serde_json", false, 6060191871448275072], [16928111194414003569, "dirs", false, 17500986238581896476], [17155886227862585100, "glob", false, 10956050299099694558]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-8a5b015c49fa8af4\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}