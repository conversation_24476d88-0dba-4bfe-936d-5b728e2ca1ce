{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 11842472800422696103, "profile": 2040997289075261528, "path": 16644724465513340381, "deps": [[806484002035136204, "gethostname", false, 10304216573231309187], [4919829919303820331, "serialize_to_javascript", false, 1631412340139035369], [5024769281214949041, "os_info", false, 16245456158809950930], [5986029879202738730, "log", false, 4278780275571731749], [9689903380558560274, "serde", false, 12791218709341986024], [10755362358622467486, "tauri", false, 742699626643638060], [10806645703491011684, "thiserror", false, 13729077771554084640], [12676100885892732016, "build_script_build", false, 9516900706161609018], [14618885535728128396, "sys_locale", false, 14227789824522876071], [15367738274754116744, "serde_json", false, 7458677227193029867]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-os-af00a491f49e0394\\dep-lib-tauri_plugin_os", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}