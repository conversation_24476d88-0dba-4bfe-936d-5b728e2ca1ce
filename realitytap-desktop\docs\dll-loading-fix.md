# librtcore.dll 加载问题修复

## 问题描述

当通过MSI安装包的"Launch RealityTap Haptics Studio"选项启动应用时，会出现librtcore初始化失败的问题，错误代码为-1。但是如果在程序安装目录下直接启动主程序，则初始化正常。

### 根本原因

原始代码中librtcore.dll的加载使用硬编码的相对路径：
```rust
let lib_path = "librtcore.dll";
```

当通过MSI安装包启动时，应用的工作目录可能不是程序安装目录，导致无法找到librtcore.dll文件。

## 解决方案

实现了多策略DLL加载机制，按以下优先级尝试加载librtcore.dll：

1. **相对路径加载**（保持向后兼容）
   - 尝试从当前工作目录加载 `librtcore.dll`

2. **可执行文件目录加载**
   - 从应用程序可执行文件所在目录加载

3. **注册表安装目录加载**（仅Windows）
   - 从Windows注册表读取安装目录：`HKCU\Software\awa\RealityTap Haptics Studio\InstallDir`
   - 从该目录加载librtcore.dll

## 技术实现

### 新增依赖

在 `Cargo.toml` 中添加了Windows注册表操作依赖：
```toml
[target.'cfg(windows)'.dependencies]
winreg = "0.52"
```

### 核心函数

1. **get_install_dir_from_registry()** - 从注册表读取安装目录
2. **try_load_library_relative()** - 相对路径加载
3. **try_load_library_from_exe_dir()** - 可执行文件目录加载
4. **try_load_library_from_registry()** - 注册表目录加载
5. **try_load_library_with_fallback()** - 多策略尝试加载

### 调试功能

在debug模式下提供了测试命令：
- **haptic_test_dll_paths()** - 测试所有DLL加载路径
- 可通过前端调用来诊断DLL加载问题

## 使用方法

### 正常使用

修复后的代码会自动尝试多种路径，无需额外配置。应用会按优先级尝试加载，并在日志中记录加载过程。

### 调试模式

在debug构建中，可以调用以下Tauri命令来测试DLL路径：
```javascript
import { invoke } from '@tauri-apps/api/core';

const testResults = await invoke('haptic_test_dll_paths');
console.log('DLL加载测试结果:', testResults);
```

## 日志记录

修复后的代码提供了详细的日志记录：
- 成功加载时记录使用的路径
- 失败时记录所有尝试的路径和错误信息
- 便于问题诊断和调试

## 兼容性

- ✅ 保持与现有代码的完全兼容
- ✅ 支持所有现有的部署方式
- ✅ 解决MSI安装包启动问题
- ✅ 跨平台兼容（注册表功能仅在Windows上可用）

## 测试建议

1. **开发环境测试**：从不同目录启动应用
2. **MSI安装测试**：通过安装包的"Launch"选项启动
3. **直接启动测试**：从安装目录直接启动exe文件
4. **日志检查**：查看DLL加载日志确认使用的路径

## 注意事项

- 注册表功能仅在Windows平台可用
- debug模式下提供额外的测试命令
- 所有策略失败时会提供详细的错误信息
- 建议在生产环境中监控DLL加载日志

## 实现状态

✅ **已完成的修改**：
1. 添加了winreg依赖到Cargo.toml
2. 实现了多策略DLL加载机制
3. 添加了Windows注册表读取功能
4. 实现了详细的日志记录
5. 添加了debug模式下的测试命令
6. 代码已成功编译并通过测试

✅ **验证结果**：
- 代码编译成功，无错误
- 所有DLL文件已正确复制到输出目录
- 多策略加载逻辑已实现
- 跨平台兼容性已确保

## 下一步建议

1. **测试验证**：在实际MSI安装环境中测试修复效果
2. **日志监控**：部署后监控DLL加载日志
3. **性能评估**：确认多策略加载不会影响启动性能
4. **文档更新**：更新用户文档说明修复内容
