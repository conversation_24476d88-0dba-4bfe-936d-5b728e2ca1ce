{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 11842472800422696103, "profile": 2241668132362809309, "path": 16644724465513340381, "deps": [[806484002035136204, "gethostname", false, 12320775638903805834], [4919829919303820331, "serialize_to_javascript", false, 18285644110342620405], [5024769281214949041, "os_info", false, 8196767569887335959], [5986029879202738730, "log", false, 9664436659979733400], [9689903380558560274, "serde", false, 18192956005055837394], [10755362358622467486, "tauri", false, 9145516495219625473], [10806645703491011684, "thiserror", false, 6725964427889589046], [12676100885892732016, "build_script_build", false, 17427154853465628288], [14618885535728128396, "sys_locale", false, 12707095293730940873], [15367738274754116744, "serde_json", false, 6060191871448275072]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-os-e6f99fead71c9f7d\\dep-lib-tauri_plugin_os", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}