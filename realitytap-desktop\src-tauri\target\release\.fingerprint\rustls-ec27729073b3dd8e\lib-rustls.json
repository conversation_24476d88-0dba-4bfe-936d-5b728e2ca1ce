{"rustc": 10895048813736897673, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1988539120246850232, "path": 11277758820835859942, "deps": [[2883436298747778685, "pki_types", false, 10724435711602842617], [3722963349756955755, "once_cell", false, 1740554149852721105], [5491919304041016563, "ring", false, 12588401802954163183], [6528079939221783635, "zeroize", false, 11957101788240356461], [7161480121686072451, "build_script_build", false, 5009095008989388474], [17003143334332120809, "subtle", false, 7149451952359377589], [17673984680181081803, "<PERSON><PERSON><PERSON>", false, 351808107311021489]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\rustls-ec27729073b3dd8e\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}