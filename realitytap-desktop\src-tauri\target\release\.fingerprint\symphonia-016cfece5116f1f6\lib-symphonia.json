{"rustc": 10895048813736897673, "features": "[\"aac\", \"adpcm\", \"default\", \"flac\", \"isomp4\", \"mkv\", \"mp3\", \"ogg\", \"pcm\", \"symphonia-bundle-flac\", \"symphonia-bundle-mp3\", \"symphonia-codec-aac\", \"symphonia-codec-adpcm\", \"symphonia-codec-pcm\", \"symphonia-codec-vorbis\", \"symphonia-format-isomp4\", \"symphonia-format-mkv\", \"symphonia-format-ogg\", \"symphonia-format-riff\", \"vorbis\", \"wav\"]", "declared_features": "[\"aac\", \"adpcm\", \"aiff\", \"alac\", \"all\", \"all-codecs\", \"all-formats\", \"caf\", \"default\", \"flac\", \"isomp4\", \"mkv\", \"mp1\", \"mp2\", \"mp3\", \"mpa\", \"ogg\", \"opt-simd\", \"opt-simd-avx\", \"opt-simd-neon\", \"opt-simd-sse\", \"pcm\", \"symphonia-bundle-flac\", \"symphonia-bundle-mp3\", \"symphonia-codec-aac\", \"symphonia-codec-adpcm\", \"symphonia-codec-alac\", \"symphonia-codec-pcm\", \"symphonia-codec-vorbis\", \"symphonia-format-caf\", \"symphonia-format-isomp4\", \"symphonia-format-mkv\", \"symphonia-format-ogg\", \"symphonia-format-riff\", \"vorbis\", \"wav\"]", "target": 6114304271110684120, "profile": 2040997289075261528, "path": 2559335228291709327, "deps": [[1190114247330347571, "symphonia_bundle_mp3", false, 14523569959972202041], [1218881066841546592, "symphonia_core", false, 4808024575692302508], [3380367994894903183, "symphonia_format_isomp4", false, 17814787509901731316], [5584775451170344503, "symphonia_format_riff", false, 12005506380086448630], [6988141578181219323, "symphonia_format_ogg", false, 7769599105981674289], [7059103048047618386, "symphonia_metadata", false, 9209889021082722929], [7532568976922888192, "symphonia_codec_vorbis", false, 17920773690271097994], [10391535624336373515, "symphonia_codec_adpcm", false, 2636615763700127911], [11709251254947567893, "symphonia_codec_pcm", false, 706717894613972220], [14888090134352227361, "symphonia_bundle_flac", false, 10584889696355927036], [16837670570652046088, "symphonia_format_mkv", false, 10439022547486698361], [17917672826516349275, "lazy_static", false, 7528862128642963678], [18095011233657354431, "symphonia_codec_aac", false, 6130007758188993485]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\symphonia-016cfece5116f1f6\\dep-lib-symphonia", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}