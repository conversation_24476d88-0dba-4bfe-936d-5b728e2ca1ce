{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 2040997289075261528, "path": 9836949865237587686, "deps": [[500211409582349667, "shared_child", false, 3639524565370338302], [1582828171158827377, "build_script_build", false, 14764696301857627182], [5138218615291878843, "tokio", false, 5553092168895511418], [5986029879202738730, "log", false, 4278780275571731749], [9451456094439810778, "regex", false, 13827262411219907142], [9689903380558560274, "serde", false, 12791218709341986024], [10755362358622467486, "tauri", false, 742699626643638060], [10806645703491011684, "thiserror", false, 13729077771554084640], [11337703028400419576, "os_pipe", false, 16708478739616640281], [14564311161534545801, "encoding_rs", false, 15221495842737274352], [15367738274754116744, "serde_json", false, 7458677227193029867], [16192041687293812804, "open", false, 10520093975428287149]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-plugin-shell-441c35815283f672\\dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}