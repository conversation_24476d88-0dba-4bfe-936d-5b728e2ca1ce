# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-available-monitors"
description = "Enables the available_monitors command without any pre-configured scope."
commands.allow = ["available_monitors"]

[[permission]]
identifier = "deny-available-monitors"
description = "Denies the available_monitors command without any pre-configured scope."
commands.deny = ["available_monitors"]
