{"rustc": 10895048813736897673, "features": "[\"common-controls-v6\", \"compression\", \"custom-protocol\", \"default\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 5408242616063297496, "profile": 1369601567987815722, "path": 13083997615113902992, "deps": [[283161442353679854, "tauri_build", false, 4144745237753284545], [11050281405049894993, "tauri_utils", false, 6165033782102807894], [13077543566650298139, "heck", false, 11309666017209925535], [17155886227862585100, "glob", false, 15036848553281000914]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-0bdd0403c49ce976\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}