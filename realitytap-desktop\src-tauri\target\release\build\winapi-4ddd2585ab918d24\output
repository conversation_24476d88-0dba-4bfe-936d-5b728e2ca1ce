cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="wmistr"
cargo:rustc-cfg=feature="lsalookup"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="evntprov"
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="vadefs"
cargo:rustc-cfg=feature="evntcons"
cargo:rustc-cfg=feature="minwindef"
cargo:rustc-cfg=feature="sspi"
cargo:rustc-cfg=feature="winnt"
cargo:rustc-cfg=feature="timezoneapi"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="devpropdef"
cargo:rustc-cfg=feature="subauth"
cargo:rustc-cfg=feature="handleapi"
cargo:rustc-cfg=feature="wincred"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-link-lib=dylib=advapi32
cargo:rustc-link-lib=dylib=credui
cargo:rustc-link-lib=dylib=kernel32
cargo:rustc-link-lib=dylib=secur32
