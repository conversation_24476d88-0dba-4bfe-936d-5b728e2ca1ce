{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 2241668132362809309, "path": 17452753421783509941, "deps": [[442785307232013896, "build_script_build", false, 16776603775144676740], [3150220818285335163, "url", false, 14863451263885477915], [4143744114649553716, "raw_window_handle", false, 3150691046013205847], [7606335748176206944, "dpi", false, 9813003235395146164], [9010263965687315507, "http", false, 2459037600811677808], [9689903380558560274, "serde", false, 18192956005055837394], [10806645703491011684, "thiserror", false, 6725964427889589046], [11050281405049894993, "tauri_utils", false, 5215172051886328680], [13116089016666501665, "windows", false, 1979446102959461390], [15367738274754116744, "serde_json", false, 6060191871448275072], [16727543399706004146, "cookie", false, 15091381076431918702]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-665c08ffd844ea25\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}