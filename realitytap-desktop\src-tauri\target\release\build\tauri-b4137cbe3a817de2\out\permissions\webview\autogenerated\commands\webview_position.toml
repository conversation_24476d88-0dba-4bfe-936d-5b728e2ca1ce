# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-webview-position"
description = "Enables the webview_position command without any pre-configured scope."
commands.allow = ["webview_position"]

[[permission]]
identifier = "deny-webview-position"
description = "Denies the webview_position command without any pre-configured scope."
commands.deny = ["webview_position"]
