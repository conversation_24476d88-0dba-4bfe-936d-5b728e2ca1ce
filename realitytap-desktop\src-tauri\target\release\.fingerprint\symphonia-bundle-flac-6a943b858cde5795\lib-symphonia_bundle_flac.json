{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 18426575973585550006, "profile": 2040997289075261528, "path": 661061965735454187, "deps": [[1218881066841546592, "symphonia_core", false, 4808024575692302508], [2423495850963981082, "symphonia_utils_xiph", false, 15104924534655553348], [5986029879202738730, "log", false, 4278780275571731749], [7059103048047618386, "symphonia_metadata", false, 9209889021082722929]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\symphonia-bundle-flac-6a943b858cde5795\\dep-lib-symphonia_bundle_flac", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}