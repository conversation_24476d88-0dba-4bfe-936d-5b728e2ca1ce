{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10755362358622467486, "build_script_build", false, 2833051112272060660], [11721252211900136025, "build_script_build", false, 5516757121051475193]], "local": [{"RerunIfChanged": {"output": "release\\build\\tauri-plugin-updater-2c0c2487aca05596\\output", "paths": ["permissions"]}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}