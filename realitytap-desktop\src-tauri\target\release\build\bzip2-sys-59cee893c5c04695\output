OPT_LEVEL = Some(3)
TARGET = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=VCINSTALLDIR
VCINSTALLDIR = None
cargo:rerun-if-env-changed=VSTEL_MSBuildProjectFullPath
VSTEL_MSBuildProjectFullPath = None
cargo:rerun-if-env-changed=VSCMD_ARG_VCVARS_SPECTRE
VSCMD_ARG_VCVARS_SPECTRE = None
cargo:rerun-if-env-changed=WindowsSdkDir
WindowsSdkDir = None
cargo:rerun-if-env-changed=WindowsSDKVersion
WindowsSDKVersion = None
cargo:rerun-if-env-changed=LIB
LIB = None
PATH = Some(E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\deps;E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release;C:\Users\<USER>\.rustup\toolchains\stable-x86_64-pc-windows-msvc\lib\rustlib\x86_64-pc-windows-msvc\lib;E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\node_modules\.bin;E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\node_modules\.bin;E:\03.Codes\10.AWA\10.realitytap\node_modules\.bin;E:\03.Codes\10.AWA\node_modules\.bin;E:\03.Codes\node_modules\.bin;E:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\PowerShell\7;C:\Program Files (x86)\VMware\VMware Workstation\bin\;C:\Python313\Scripts\;C:\Python313\;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\bin;C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.6\libnvvp;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files (x86)\ZeroTier\One\;C:\Program Files\Java\jdk-17\bin;D:\01.Apps\ffmpeg\bin;D:\04.Android\Sdk\platform-tools;D:\01.Apps\lz4;E:\03.Codes\06.bat;D:\07.Tools\bin;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\dotnet\;C:\Users\<USER>\.cargo\bin\;C:\WINDOWS\system32\WindowsPowerShell\v1.0\;C:\Program Files\BinDiff\bin;C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.39.33519\bin\Hostx64\x64\;D:\04Huawei\DevEco Studio\sdk\HarmonyOS-NEXT-DB1\openharmony\toolchains;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\01.Apps\radare2\bin;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Program Files\Git\cmd;C:\Program Files\NVIDIA Corporation\Nsight Compute 2024.3.2\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\nodejs\;C:\ProgramData\chocolatey\bin;C:\Users\<USER>\AppData\Roaming\Python\Python313\Scripts\;D:\01.Apps\WixTools\;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\PowerShell\7\;C:\Program Files\CMake\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;D:\04.Android\IntelliJ IDEA 2025.1.1\bin;;D:\04.Android\IntelliJ IDEA Community Edition 2024.1.1\bin;;%DevEco Studio%;C:\Users\<USER>\AppData\Roaming\npm)
cargo:rerun-if-env-changed=INCLUDE
INCLUDE = None
HOST = Some(x86_64-pc-windows-msvc)
cargo:rerun-if-env-changed=CC_x86_64-pc-windows-msvc
CC_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=CC_x86_64_pc_windows_msvc
CC_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_CC
HOST_CC = None
cargo:rerun-if-env-changed=CC
CC = None
cargo:rerun-if-env-changed=CRATE_CC_NO_DEFAULTS
CRATE_CC_NO_DEFAULTS = None
CARGO_CFG_TARGET_FEATURE = Some(cmpxchg16b,fxsr,sse,sse2,sse3)
DEBUG = Some(false)
cargo:rerun-if-env-changed=CFLAGS
CFLAGS = None
cargo:rerun-if-env-changed=HOST_CFLAGS
HOST_CFLAGS = None
cargo:rerun-if-env-changed=CFLAGS_x86_64_pc_windows_msvc
CFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=CFLAGS_x86_64-pc-windows-msvc
CFLAGS_x86_64-pc-windows-msvc = None
CARGO_ENCODED_RUSTFLAGS = Some()
cargo:rerun-if-env-changed=CC_ENABLE_DEBUG_OUTPUT
blocksort.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): warning C4005: 'BZ_EXPORT': macro redefinition
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): note: 'BZ_EXPORT' previously declared on the command line
huffman.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): warning C4005: 'BZ_EXPORT': macro redefinition
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): note: 'BZ_EXPORT' previously declared on the command line
crctable.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): warning C4005: 'BZ_EXPORT': macro redefinition
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): note: 'BZ_EXPORT' previously declared on the command line
randtable.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): warning C4005: 'BZ_EXPORT': macro redefinition
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): note: 'BZ_EXPORT' previously declared on the command line
compress.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): warning C4005: 'BZ_EXPORT': macro redefinition
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): note: 'BZ_EXPORT' previously declared on the command line
decompress.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): warning C4005: 'BZ_EXPORT': macro redefinition
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): note: 'BZ_EXPORT' previously declared on the command line
bzlib.c
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): warning C4005: 'BZ_EXPORT': macro redefinition
C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\bzip2-sys-0.1.13+1.0.8\bzip2-1.0.8\bzlib.h(70): note: 'BZ_EXPORT' previously declared on the command line
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
exit code: 0
cargo:rerun-if-env-changed=AR_x86_64-pc-windows-msvc
AR_x86_64-pc-windows-msvc = None
cargo:rerun-if-env-changed=AR_x86_64_pc_windows_msvc
AR_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=HOST_AR
HOST_AR = None
cargo:rerun-if-env-changed=AR
AR = None
cargo:rerun-if-env-changed=ARFLAGS
ARFLAGS = None
cargo:rerun-if-env-changed=HOST_ARFLAGS
HOST_ARFLAGS = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64_pc_windows_msvc
ARFLAGS_x86_64_pc_windows_msvc = None
cargo:rerun-if-env-changed=ARFLAGS_x86_64-pc-windows-msvc
ARFLAGS_x86_64-pc-windows-msvc = None
cargo:rustc-link-search=native=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC\14.43.34808\atlmfc\lib\x64
cargo:rustc-link-lib=static=bz2
cargo:rustc-link-search=native=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\build\bzip2-sys-59cee893c5c04695\out\lib
cargo:root=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\build\bzip2-sys-59cee893c5c04695\out
cargo:include=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\release\build\bzip2-sys-59cee893c5c04695\out\include
