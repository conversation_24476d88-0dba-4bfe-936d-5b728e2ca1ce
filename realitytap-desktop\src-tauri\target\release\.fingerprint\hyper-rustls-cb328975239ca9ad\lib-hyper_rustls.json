{"rustc": 10895048813736897673, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 2040997289075261528, "path": 16165578117009479615, "deps": [[784494742817713399, "tower_service", false, 6036151208506612056], [2883436298747778685, "pki_types", false, 10724435711602842617], [4942430025333810336, "webpki_roots", false, 16995180255850554049], [5138218615291878843, "tokio", false, 5553092168895511418], [7161480121686072451, "rustls", false, 11627926985648990647], [9010263965687315507, "http", false, 537038018071154330], [10595802073777078462, "hyper_util", false, 13452558322726807349], [11895591994124935963, "tokio_rustls", false, 17712020327643199050], [11957360342995674422, "hyper", false, 3675683039769659276]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\hyper-rustls-cb328975239ca9ad\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}