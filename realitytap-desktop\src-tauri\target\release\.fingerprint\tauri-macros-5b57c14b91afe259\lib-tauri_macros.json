{"rustc": 10895048813736897673, "features": "[\"compression\", \"custom-protocol\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 1369601567987815722, "path": 15262338241553268757, "deps": [[3060637413840920116, "proc_macro2", false, 2224750471340170475], [7341521034400937459, "tauri_codegen", false, 6967644555021621150], [11050281405049894993, "tauri_utils", false, 6165033782102807894], [13077543566650298139, "heck", false, 11309666017209925535], [17990358020177143287, "quote", false, 15310116662474033973], [18149961000318489080, "syn", false, 13850413847745182672]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-macros-5b57c14b91afe259\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}