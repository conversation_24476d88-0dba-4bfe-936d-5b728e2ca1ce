# DLL加载测试脚本
# 用于验证多策略DLL加载修复是否正常工作

Write-Host "=== RealityTap DLL加载测试 ===" -ForegroundColor Green

# 获取当前脚本目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ExeDir = Join-Path $ScriptDir "src-tauri\target\debug"
$ExePath = Join-Path $ExeDir "realitytap_studio.exe"

Write-Host "脚本目录: $ScriptDir" -ForegroundColor Yellow
Write-Host "可执行文件目录: $ExeDir" -ForegroundColor Yellow
Write-Host "可执行文件路径: $ExePath" -ForegroundColor Yellow

# 检查可执行文件是否存在
if (-not (Test-Path $ExePath)) {
    Write-Host "错误: 找不到可执行文件 $ExePath" -ForegroundColor Red
    Write-Host "请先运行 'cargo build' 编译项目" -ForegroundColor Red
    exit 1
}

# 检查DLL文件是否存在
$RequiredDlls = @("librtcore.dll", "librtssl.dll", "librtutils.dll")
Write-Host "`n检查必需的DLL文件..." -ForegroundColor Cyan

foreach ($dll in $RequiredDlls) {
    $dllPath = Join-Path $ExeDir $dll
    if (Test-Path $dllPath) {
        Write-Host "✓ $dll 存在" -ForegroundColor Green
    } else {
        Write-Host "✗ $dll 缺失" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试1: 从可执行文件目录启动 ===" -ForegroundColor Cyan
Write-Host "这应该使用策略2（可执行文件目录加载）成功" -ForegroundColor Gray

# 切换到可执行文件目录并启动
Push-Location $ExeDir
try {
    Write-Host "当前工作目录: $(Get-Location)" -ForegroundColor Yellow
    Write-Host "启动应用程序（3秒后自动关闭）..." -ForegroundColor Yellow

    # 启动应用程序并等待3秒
    $process = Start-Process -FilePath ".\realitytap_studio.exe" -PassThru -WindowStyle Minimized
    Start-Sleep -Seconds 3

    if ($process -and -not $process.HasExited) {
        $process.Kill()
        Write-Host "✓ 应用程序成功启动并运行" -ForegroundColor Green
    } else {
        Write-Host "✗ 应用程序启动后立即退出" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Pop-Location
}

Write-Host "`n=== 测试2: 从不同目录启动 ===" -ForegroundColor Cyan
Write-Host "这应该使用策略3（注册表目录加载）或策略2成功" -ForegroundColor Gray

# 切换到临时目录并启动
$TempDir = $env:TEMP
Push-Location $TempDir
try {
    Write-Host "当前工作目录: $(Get-Location)" -ForegroundColor Yellow
    Write-Host "启动应用程序（5秒后自动关闭）..." -ForegroundColor Yellow
    
    # 启动应用程序并等待5秒
    $process = Start-Process -FilePath $ExePath -PassThru -WindowStyle Minimized
    Start-Sleep -Seconds 5
    
    if (-not $process.HasExited) {
        $process.Kill()
        Write-Host "✓ 应用程序成功启动并运行" -ForegroundColor Green
    } else {
        Write-Host "✗ 应用程序启动后立即退出" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
} finally {
    Pop-Location
}

Write-Host "`n=== 测试3: 检查注册表项 ===" -ForegroundColor Cyan

$RegPath = "HKCU:\Software\awa\RealityTap Haptics Studio"
if (Test-Path $RegPath) {
    Write-Host "✓ 注册表项存在: $RegPath" -ForegroundColor Green
    
    try {
        $InstallDir = Get-ItemProperty -Path $RegPath -Name "InstallDir" -ErrorAction SilentlyContinue
        if ($InstallDir) {
            Write-Host "✓ InstallDir 值: $($InstallDir.InstallDir)" -ForegroundColor Green
        } else {
            Write-Host "⚠ InstallDir 值不存在，将使用默认值" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "⚠ 无法读取 InstallDir 值" -ForegroundColor Yellow
    }
} else {
    Write-Host "⚠ 注册表项不存在: $RegPath" -ForegroundColor Yellow
    Write-Host "  这是正常的，因为我们在开发环境中测试" -ForegroundColor Gray
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果所有测试都通过，说明DLL加载修复工作正常" -ForegroundColor Green
Write-Host "建议在实际MSI安装环境中进行最终验证" -ForegroundColor Yellow

# 暂停以便查看结果
Write-Host "`n按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
