{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 2040997289075261528, "path": 9551424322180838343, "deps": [[376837177317575824, "build_script_build", false, 14868804458489412197], [4143744114649553716, "raw_window_handle", false, 826242152692617864], [5986029879202738730, "log", false, 4278780275571731749], [10281541584571964250, "windows_sys", false, 18331969135627849852]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\softbuffer-69360fc355058aaf\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}