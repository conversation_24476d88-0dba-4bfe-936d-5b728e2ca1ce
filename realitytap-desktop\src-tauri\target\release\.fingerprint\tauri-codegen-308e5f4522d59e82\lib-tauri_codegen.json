{"rustc": 10895048813736897673, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\", \"regex\"]", "target": 17460618180909919773, "profile": 1369601567987815722, "path": 17642775920930496750, "deps": [[3060637413840920116, "proc_macro2", false, 2224750471340170475], [3150220818285335163, "url", false, 8003661020387016638], [4899080583175475170, "semver", false, 12183985207572930630], [7170110829644101142, "json_patch", false, 2064230335387406586], [7392050791754369441, "ico", false, 11610590366314650885], [8269115081296425610, "uuid", false, 9213008617362214269], [9689903380558560274, "serde", false, 12432392799593878276], [9857275760291862238, "sha2", false, 6070872133278771873], [10806645703491011684, "thiserror", false, 9393090103709299999], [11050281405049894993, "tauri_utils", false, 6165033782102807894], [12687914511023397207, "png", false, 10468045638591267], [13077212702700853852, "base64", false, 5138858723638289520], [14132538657330703225, "brotli", false, 13458612873346433647], [15367738274754116744, "serde_json", false, 7653967626044807545], [15622660310229662834, "walkdir", false, 1977710270525059799], [17990358020177143287, "quote", false, 15310116662474033973], [18149961000318489080, "syn", false, 13850413847745182672]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tauri-codegen-308e5f4522d59e82\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}