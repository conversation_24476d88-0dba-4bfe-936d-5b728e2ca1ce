{"rustc": 10895048813736897673, "features": "[\"any_impl\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_impl\", \"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-rs-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-default\", \"zlib-ng\", \"zlib-ng-compat\", \"zlib-rs\"]", "target": 6173716359330453699, "profile": 2040997289075261528, "path": 1766855620247933048, "deps": [[4675849561795547236, "miniz_oxide", false, 14438130187348996860], [5466618496199522463, "crc32fast", false, 16616865325400842343]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\flate2-82289cd7677c9e6b\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}