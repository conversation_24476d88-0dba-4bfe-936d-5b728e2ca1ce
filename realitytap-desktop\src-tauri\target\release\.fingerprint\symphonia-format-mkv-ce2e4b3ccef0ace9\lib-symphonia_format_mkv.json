{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 3153802102509654810, "profile": 2040997289075261528, "path": 15127729195080619922, "deps": [[1218881066841546592, "symphonia_core", false, 4808024575692302508], [2423495850963981082, "symphonia_utils_xiph", false, 15104924534655553348], [5986029879202738730, "log", false, 4278780275571731749], [7059103048047618386, "symphonia_metadata", false, 9209889021082722929], [17917672826516349275, "lazy_static", false, 7528862128642963678]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\symphonia-format-mkv-ce2e4b3ccef0ace9\\dep-lib-symphonia_format_mkv", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}